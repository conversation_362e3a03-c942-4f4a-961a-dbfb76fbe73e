#Training
learning_rate: 0.0005
batch_size: 2
weight_decay: 0.01
lr_decay: 0.99
epochs: 90
train_2d: False

# Model
model_name: OptimizedTCPFormer
n_layers: 16
dim_in: 3
dim_feat: 128
dim_rep: 512
dim_out: 3
mlp_ratio: 4
act_layer: gelu
attn_drop: 0.0
drop: 0.0
drop_path: 0.0
use_layer_scale: True
layer_scale_init_value: 0.00001
use_adaptive_fusion: True
num_heads: 8
qkv_bias: False
qkv_scale: null
hierarchical: False
use_temporal_similarity: True 
neighbour_num: 2  
temporal_connection_len: 1 
use_tcn: False
graph_only: False
n_frames: 243 

# Optimized Frequency Features
freq_ratio: 0.5  # General frequency ratio
dst_freq_ratio: 0.3  # DSTFormer frequency ratio (focus on low frequencies)
memory_freq_ratio: 0.7  # MemoryInduced frequency ratio (broader frequency range)
use_joint_flow: True
motion_dim: 64
joint_flow_dropout: 0.1

# Data
data_root: data/motion3d/
data_root_2d: data/motion2d/
subset_list: [ H36M-243 ]
dt_file: h36m_sh_conf_cam_source_final.pkl
num_joints: 17
root_rel: True 
add_velocity: False

# Loss
lambda_3d_velocity: 20.0
lambda_scale: 0.5
lambda_lv: 0.0
lambda_lg: 0.0
lambda_a: 0.0
lambda_av: 0.0
lambda_mi: 0.001

# Augmentation
use_proj_as_2d: False
flip: True
